use anyhow::{anyhow, Result};
use ed25519_dalek::{Signing<PERSON><PERSON>, Verifying<PERSON><PERSON>};
use pkarr::{Keypair, PublicKey, SignedPacket};

/// Pkarr integration for publishing and resolving DNS records
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct PkarrClient {
    client: pkarr::Client,
}

impl PkarrClient {
    /// Create a new PkarrClient
    pub fn new() -> Result<Self> {
        Ok(Self {
            client: pkarr::Client::builder().build()?,
        })
    }

    /// Publish a query public key to pkarr DNS
    ///
    /// # Arguments
    /// * `pkarr_keypair` - The pkarr keypair to sign the DNS record
    /// * `query_public_key` - The Ed25519 public key to publish
    /// * `subdomain` - Optional subdomain for the record (defaults to "query")
    ///
    /// # Returns
    /// The pkarr public key that was used to publish the record
    pub async fn publish_query_key(
        &self,
        pkarr_keypair: &Keypair,
        query_public_key: &Verifying<PERSON>ey,
        subdomain: Option<&str>,
    ) -> Result<PublicKey> {
        let subdomain = subdomain.unwrap_or("query");

        // Encode the Ed25519 public key as TXT record
        let public_key_hex = hex::encode(query_public_key.as_bytes());
        let txt_data = format!("ed25519={}", public_key_hex);

        // Create and sign the DNS packet using the builder pattern
        let signed_packet = SignedPacket::builder()
            .txt(subdomain.try_into().unwrap(), txt_data.as_str().try_into().unwrap(), 300)
            .sign(pkarr_keypair)?;

        // Publish the packet
        self.client.publish(&signed_packet, None).await?;

        println!("Published query key to pkarr: {}", pkarr_keypair.public_key().to_z32());
        println!("Query public key: {}", public_key_hex);

        Ok(pkarr_keypair.public_key())
    }

    /// Resolve a query public key from pkarr DNS
    ///
    /// # Arguments
    /// * `pkarr_public_key` - The pkarr public key to resolve from
    /// * `subdomain` - Optional subdomain for the record (defaults to "query")
    ///
    /// # Returns
    /// The Ed25519 public key if found
    pub async fn resolve_query_key(
        &self,
        pkarr_public_key: &PublicKey,
        subdomain: Option<&str>,
    ) -> Result<VerifyingKey> {
        let subdomain = subdomain.unwrap_or("query");

        // Resolve the DNS record
        let signed_packet = match self.client.resolve(pkarr_public_key).await {
            Some(packet) => packet,
            None => return Err(anyhow!("No DNS record found for pkarr key: {}", pkarr_public_key.to_z32())),
        };

        // Look for TXT record with our subdomain
        for record in signed_packet.resource_records(subdomain) {
            if let pkarr::dns::rdata::RData::TXT(txt_data) = &record.rdata {
                let txt_string: String = txt_data.clone().try_into()
                    .map_err(|e| anyhow!("Failed to convert TXT to string: {:?}", e))?;

                // Parse ed25519=<hex> format
                if let Some(hex_part) = txt_string.strip_prefix("ed25519=") {
                    let public_key_bytes = hex::decode(hex_part)
                        .map_err(|e| anyhow!("Failed to decode hex public key: {}", e))?;

                    if public_key_bytes.len() != 32 {
                        return Err(anyhow!("Invalid public key length: expected 32 bytes, got {}", public_key_bytes.len()));
                    }

                    let mut key_bytes = [0u8; 32];
                    key_bytes.copy_from_slice(&public_key_bytes);

                    let verifying_key = VerifyingKey::from_bytes(&key_bytes)
                        .map_err(|e| anyhow!("Failed to create VerifyingKey: {}", e))?;

                    println!("Resolved query key from pkarr: {}", pkarr_public_key.to_z32());
                    println!("Query public key: {}", hex::encode(key_bytes));

                    return Ok(verifying_key);
                }
            }
        }

        Err(anyhow!("No ed25519 public key found in DNS record for subdomain: {}", subdomain))
    }
}

impl Default for PkarrClient {
    fn default() -> Self {
        Self::new().expect("Failed to create default PkarrClient")
    }
}

/// Convert Ed25519 SigningKey to pkarr Keypair
pub fn signing_key_to_pkarr_keypair(signing_key: &SigningKey) -> Keypair {
    Keypair::from_secret_key(&signing_key.to_bytes())
}

/// Convert Ed25519 VerifyingKey to pkarr PublicKey
pub fn verifying_key_to_pkarr_public_key(verifying_key: &VerifyingKey) -> PublicKey {
    PublicKey::try_from(&verifying_key.to_bytes()).expect("Valid Ed25519 public key")
}

#[cfg(test)]
mod tests {
    use super::*;
    use ed25519_dalek::SigningKey;
    use rand::rngs::OsRng;

    #[tokio::test]
    async fn test_pkarr_key_conversion() {
        let signing_key = SigningKey::generate(&mut OsRng);
        let verifying_key = signing_key.verifying_key();
        
        let pkarr_keypair = signing_key_to_pkarr_keypair(&signing_key);
        let pkarr_public_key = verifying_key_to_pkarr_public_key(&verifying_key);
        
        assert_eq!(pkarr_keypair.public_key(), pkarr_public_key);
    }
}
