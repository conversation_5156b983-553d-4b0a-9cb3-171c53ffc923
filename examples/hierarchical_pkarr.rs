use anyhow::Result;
use ed25519_dalek::{SigningKey, VerifyingKey};
use iroh_topic_tracker::integrations::pkarr::PkarrClient;
use pkarr::Keypair;

// Simplified key derivation for this example
use curve25519_dalek::{scalar::Scalar, constants::ED25519_BASEPOINT_POINT};
use hmac::{Hmac, Mac};
use sha2::{Digest, Sha256, Sha512};

type HmacSha512 = Hmac<Sha512>;

const CHAIN_CODE_LENGTH: usize = 32;
const HARDENED_OFFSET: u32 = 0x80000000;

#[derive(Debug, <PERSON>lone)]
pub struct ExtendedKey {
    key_left: [u8; 32],
    key_right: [u8; 32],
    chain_code: [u8; CHAIN_CODE_LENGTH],
    public_key: VerifyingKey,
}

#[derive(Debug)]
pub enum KeyDerivationError {
    InvalidKey,
    CryptoError,
}

impl std::fmt::Display for KeyDerivationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            KeyDerivationError::InvalidKey => write!(f, "Invalid key (third highest bit set)"),
            KeyDerivationError::CryptoError => write!(f, "Cryptographic error"),
        }
    }
}

impl std::error::Error for KeyDerivationError {}

impl ExtendedKey {
    pub fn from_seed(seed: &[u8]) -> Result<Self, KeyDerivationError> {
        let mut mac = HmacSha512::new_from_slice(b"ed25519 seed")
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(seed);
        let master_key = mac.finalize().into_bytes();
        
        let mut key_left = [0u8; 32];
        let mut key_right = [0u8; 32];
        key_left.copy_from_slice(&master_key[..32]);
        key_right.copy_from_slice(&master_key[32..]);
        
        if (key_left[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        Self::clamp_scalar(&mut key_left);
        
        let scalar = Scalar::from_bytes_mod_order(key_left);
        let public_point = &scalar * &ED25519_BASEPOINT_POINT;
        let public_key = VerifyingKey::from_bytes(&public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&master_key);
        let chain_code_hash = chain_hasher.finalize();
        let mut chain_code = [0u8; CHAIN_CODE_LENGTH];
        chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(ExtendedKey {
            key_left,
            key_right,
            chain_code,
            public_key,
        })
    }
    
    pub fn derive_child(&self, index: u32) -> Result<ExtendedKey, KeyDerivationError> {
        let is_hardened = index >= HARDENED_OFFSET;
        
        let mut hmac_input = Vec::new();
        if is_hardened {
            hmac_input.push(0x00);
            hmac_input.extend_from_slice(&self.key_left);
            hmac_input.extend_from_slice(&self.key_right);
        } else {
            hmac_input.push(0x02);
            hmac_input.extend_from_slice(self.public_key.as_bytes());
        }
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        let mut mac = HmacSha512::new_from_slice(&self.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(&hmac_input);
        let hmac_result = mac.finalize().into_bytes();
        
        let mut kl_new = [0u8; 32];
        let mut kr_new = [0u8; 32];
        kl_new.copy_from_slice(&hmac_result[..32]);
        kr_new.copy_from_slice(&hmac_result[32..]);
        
        if (kl_new[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        Self::clamp_scalar(&mut kl_new);
        
        let child_scalar = Scalar::from_bytes_mod_order(kl_new);
        let child_public_point = &child_scalar * &ED25519_BASEPOINT_POINT;
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&hmac_result);
        let chain_code_hash = chain_hasher.finalize();
        let mut child_chain_code = [0u8; CHAIN_CODE_LENGTH];
        child_chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(ExtendedKey {
            key_left: kl_new,
            key_right: kr_new,
            chain_code: child_chain_code,
            public_key: child_public_key,
        })
    }
    
    pub fn signing_key(&self) -> SigningKey {
        SigningKey::from_bytes(&self.key_left)
    }
    
    fn clamp_scalar(scalar: &mut [u8; 32]) {
        scalar[0] &= 248;
        scalar[31] &= 127;
        scalar[31] |= 64;
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== Hierarchical Pkarr Key Management ===\n");
    
    // Step 1: Generate root pkarr keypair
    let pkarr_keypair = Keypair::random();
    println!("1. Root pkarr keypair: {}", pkarr_keypair.public_key().to_z32());
    
    // Step 2: Derive query master key using hardened derivation (m/0')
    let seed = pkarr_keypair.secret_key();
    let mut counter = 0u32;
    let root_key = loop {
        let mut extended_seed = seed.to_vec();
        extended_seed.extend_from_slice(&counter.to_le_bytes());
        
        match ExtendedKey::from_seed(&extended_seed) {
            Ok(key) => break key,
            Err(KeyDerivationError::InvalidKey) => {
                counter += 1;
                if counter > 1000 {
                    return Err(anyhow::anyhow!("Could not find valid root key"));
                }
            }
            Err(e) => return Err(anyhow::anyhow!("Error: {:?}", e)),
        }
    };
    
    // Try different hardened indices until we find a valid one
    let mut hardened_index = 0u32;
    let query_master_key = loop {
        match root_key.derive_child(HARDENED_OFFSET + hardened_index) {
            Ok(key) => {
                println!("2. Query master key (m/{}'): {}", hardened_index, hex::encode(key.public_key.as_bytes()));
                break key;
            }
            Err(KeyDerivationError::InvalidKey) => {
                hardened_index += 1;
                if hardened_index > 100 {
                    return Err(anyhow::anyhow!("Could not find valid query master key"));
                }
            }
            Err(e) => return Err(anyhow::anyhow!("Error: {:?}", e)),
        }
    };
    
    // Step 3: Publish query master public key to pkarr
    let pkarr_client = PkarrClient::new()?;
    pkarr_client.publish_query_key(
        &pkarr_keypair,
        &query_master_key.public_key,
        Some("query-master"),
    ).await?;
    println!("3. ✓ Published query master public key to pkarr");
    
    // Step 4: Derive specific purpose keys using non-hardened derivation
    let purposes = vec![
        "user-data",
        "messages",
        "metadata",
        "files",
        "social",
    ];

    println!("\n4. Deriving purpose-specific keys (non-hardened):");

    let mut successful_purposes = Vec::new();

    for (purpose_idx, purpose) in purposes.iter().enumerate() {
        // Start each purpose at a different base index to ensure uniqueness
        let mut index = (purpose_idx as u32) * 20; // Space them out
        let mut found_valid_key = false;

        while index < (purpose_idx as u32) * 20 + 100 && !found_valid_key {
            match query_master_key.derive_child(index) {
                Ok(purpose_key) => {
                    println!("   ✓ {}: m/{}'/{} → {}",
                        purpose,
                        hardened_index,
                        index,
                        hex::encode(purpose_key.public_key.as_bytes())[..16].to_string() + "..."
                    );

                    // Publish some example data under this purpose key
                    let purpose_keypair = pkarr::Keypair::from_secret_key(&purpose_key.signing_key().to_bytes());

                    // Publish a simple record indicating this purpose
                    pkarr_client.publish_query_key(
                        &purpose_keypair,
                        &purpose_key.public_key, // Self-reference for demo
                        Some("info"),
                    ).await?;

                    println!("     → Published example record at: {}", purpose_keypair.public_key().to_z32());

                    successful_purposes.push((purpose.to_string(), index, purpose_keypair.public_key()));
                    found_valid_key = true;
                }
                Err(KeyDerivationError::InvalidKey) => {
                    index += 1; // Try next index
                }
                Err(e) => {
                    println!("   ✗ {}: Unexpected error - {}", purpose, e);
                    break;
                }
            }
        }

        if !found_valid_key {
            println!("   ✗ {}: Could not find valid key after 100 attempts", purpose);
        }
    }
    
    println!("\n=== Security Model ===");
    println!("🔒 Server Security:");
    println!("   • Has root pkarr private key → can derive all keys");
    println!("   • Has query master private key → can derive all purpose keys");
    println!("   • Can publish/update records for any purpose");
    
    println!("\n🔓 Client Capabilities:");
    println!("   • Has root pkarr public key → can resolve query master");
    println!("   • Has query master public key → can derive purpose public keys");
    println!("   • Can resolve records for any purpose");
    println!("   • CANNOT derive any private keys (hardened protection)");
    println!("   • CANNOT overwrite any records (no private keys)");
    
    println!("\n=== Client Workflow ===");
    println!("1. Client gets root pkarr public key: {}", pkarr_keypair.public_key().to_z32());
    println!("2. Client resolves query master from pkarr DNS");
    println!("3. Client derives purpose public keys: m/0, m/1, m/2, ...");
    println!("4. Client resolves specific records using derived keys");
    
    println!("\n=== Example Use Cases ===");
    println!("• Social app: Derive keys for posts, messages, profile, friends");
    println!("• File storage: Derive keys for documents, images, backups, shared");
    println!("• Identity: Derive keys for credentials, attestations, proofs, metadata");
    println!("• Messaging: Derive keys for contacts, groups, channels, archives");
    
    println!("\n✅ Hierarchical pkarr key management setup complete!");
    println!("   Run the pkarr_client example to see client-side resolution");
    
    Ok(())
}
