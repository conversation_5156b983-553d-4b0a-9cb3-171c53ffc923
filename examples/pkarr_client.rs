use anyhow::Result;
use ed25519_dalek::VerifyingKey;
use iroh_topic_tracker::integrations::pkarr::PkarrClient;
use pkarr::PublicKey;

// For public key derivation, we need these imports
use curve25519_dalek::{scalar::Scalar, constants::ED25519_BASEPOINT_POINT, edwards::CompressedEdwardsY};
use hmac::{Hmac, Mac};
use sha2::{Digest, Sha256, Sha512};

type HmacSha512 = Hmac<Sha512>;

const CHAIN_CODE_LENGTH: usize = 32;
const HARDENED_OFFSET: u32 = 0x80000000;

#[derive(Debug, <PERSON>lone)]
pub struct PublicExtendedKey {
    public_key: VerifyingKey,
    chain_code: [u8; CHAIN_CODE_LENGTH],
}

#[derive(Debug)]
pub enum KeyDerivationError {
    InvalidKey,
    InvalidChildIndex,
    CryptoError,
    KeyAtInfinity,
    HardenedDerivationNotSupported,
}

impl std::fmt::Display for KeyDerivationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            KeyDerivationError::InvalidKey => write!(f, "Invalid key (third highest bit set)"),
            KeyDerivationError::InvalidChildIndex => write!(f, "Invalid child index"),
            KeyDerivationError::CryptoError => write!(f, "Cryptographic error"),
            KeyDerivationError::KeyAtInfinity => write!(f, "Key at infinity"),
            KeyDerivationError::HardenedDerivationNotSupported => write!(f, "Hardened derivation not supported for public keys"),
        }
    }
}

impl std::error::Error for KeyDerivationError {}

impl PublicExtendedKey {
    /// Create a public extended key from a root public key and chain code
    /// This simulates what a client would have: the root public key and chain code
    pub fn from_public_key_and_chain_code(public_key: VerifyingKey, chain_code: [u8; 32]) -> Self {
        Self {
            public_key,
            chain_code,
        }
    }
    
    /// Derive child public key at given index (non-hardened only)
    pub fn derive_child(&self, index: u32) -> Result<PublicExtendedKey, KeyDerivationError> {
        if index >= HARDENED_OFFSET {
            return Err(KeyDerivationError::HardenedDerivationNotSupported);
        }
        
        // Non-hardened derivation: 0x02 || A || index
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(self.public_key.as_bytes());
        hmac_input.extend_from_slice(&index.to_be_bytes());
        
        // Compute HMAC
        let mut mac = HmacSha512::new_from_slice(&self.chain_code)
            .map_err(|_| KeyDerivationError::CryptoError)?;
        mac.update(&hmac_input);
        let hmac_result = mac.finalize().into_bytes();
        
        // Split result
        let mut kl_new = [0u8; 32];
        kl_new.copy_from_slice(&hmac_result[..32]);
        
        // Check third highest bit requirement
        if (kl_new[31] & 0x20) != 0 {
            return Err(KeyDerivationError::InvalidKey);
        }
        
        // Apply Ed25519 bit manipulations
        Self::clamp_scalar(&mut kl_new);
        
        // Generate child scalar
        let child_scalar = Scalar::from_bytes_mod_order(kl_new);
        
        // For public key derivation: child_public = parent_public + child_scalar * G
        let compressed_parent = CompressedEdwardsY(*self.public_key.as_bytes());
        let parent_point = compressed_parent.decompress()
            .ok_or(KeyDerivationError::CryptoError)?;
        let child_public_point = parent_point + &child_scalar * &ED25519_BASEPOINT_POINT;
        
        // Check if public key is identity point (should discard)
        if child_public_point.compress().to_bytes() == [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] {
            return Err(KeyDerivationError::KeyAtInfinity);
        }
        
        let child_public_key = VerifyingKey::from_bytes(&child_public_point.compress().to_bytes())
            .map_err(|_| KeyDerivationError::CryptoError)?;
        
        // Generate child chain code
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(&[0x01]);
        chain_hasher.update(&hmac_result);
        let chain_code_hash = chain_hasher.finalize();
        let mut child_chain_code = [0u8; CHAIN_CODE_LENGTH];
        child_chain_code.copy_from_slice(&chain_code_hash);
        
        Ok(PublicExtendedKey {
            public_key: child_public_key,
            chain_code: child_chain_code,
        })
    }
    
    /// Derive child key from derivation path (non-hardened only)
    pub fn derive_path(&self, path: &str) -> Result<PublicExtendedKey, KeyDerivationError> {
        let path = path.strip_prefix("m/").unwrap_or(path);
        let mut current_key = self.clone();
        
        for component in path.split('/') {
            if component.is_empty() {
                continue;
            }
            
            // Check if it's hardened (ends with ' or h)
            if component.ends_with('\'') || component.ends_with('h') {
                return Err(KeyDerivationError::HardenedDerivationNotSupported);
            }
            
            let index: u32 = component.parse()
                .map_err(|_| KeyDerivationError::InvalidChildIndex)?;
            
            current_key = current_key.derive_child(index)?;
        }
        
        Ok(current_key)
    }
    
    /// Get the public key
    pub fn public_key(&self) -> &VerifyingKey {
        &self.public_key
    }
    
    /// Apply Ed25519 scalar clamping
    fn clamp_scalar(scalar: &mut [u8; 32]) {
        scalar[0] &= 248;  // Clear bottom 3 bits
        scalar[31] &= 127; // Clear top bit
        scalar[31] |= 64;  // Set second highest bit
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("=== Pkarr Client-side Workflow ===");
    
    // Step 1: Take root public key (this would come from the server or be known)
    // For this example, we'll use a known pkarr public key from the server example
    // In practice, this would be provided by the user or discovered through some mechanism
    
    // You can get this from running the server example
    let pkarr_public_key_str = "sjs59ec5s5x8wkjbkwicgd1xi8who5cfkqe6hun63nop7zrszfjy"; // Example from server
    let pkarr_public_key = PublicKey::try_from(pkarr_public_key_str)?;
    
    println!("1. Using pkarr public key: {}", pkarr_public_key.to_z32());
    
    // Step 2: Derive query public key using public key derivation
    // We need to simulate the same process the server used, but with public key only
    
    // For this example, we'll assume we know the derivation parameters used by the server
    // In practice, these might be standardized or communicated through the protocol
    
    // The server used the pkarr secret key as seed, then derived m/4' (hardened)
    // Since we can't do hardened derivation with public keys, we'll use a different approach
    
    // Alternative approach: Use non-hardened derivation from a known root
    // For demonstration, let's assume the server also publishes a non-hardened derivation
    
    println!("2. Note: This example demonstrates the concept, but requires coordination with server");
    println!("   for the actual derivation path and root public key to use.");
    
    // Step 3: Resolve query public key record from pkarr
    let pkarr_client = PkarrClient::new()?;
    
    match pkarr_client.resolve_query_key(&pkarr_public_key, Some("query")).await {
        Ok(resolved_key) => {
            println!("3. ✓ Successfully resolved query key from pkarr!");
            println!("   Query public key: {}", hex::encode(resolved_key.as_bytes()));
            
            // This is the key that would be used for further operations
            println!("4. ✓ Client can now use this query key for operations");
        }
        Err(e) => {
            println!("3. ✗ Failed to resolve query key: {}", e);
            println!("   Make sure the server example has been run first to publish the key");
        }
    }
    
    // Demonstrate the concept of public key derivation (even though we can't match the server's hardened path)
    println!("\n=== Demonstrating Public Key Derivation Concept ===");
    
    // Create a dummy root public key and chain code for demonstration
    let dummy_root_key = ed25519_dalek::SigningKey::generate(&mut rand::rngs::OsRng).verifying_key();
    let dummy_chain_code = [42u8; 32]; // In practice, this would be derived or known
    
    let public_extended_key = PublicExtendedKey::from_public_key_and_chain_code(dummy_root_key, dummy_chain_code);
    
    // Try to derive a non-hardened child key
    match public_extended_key.derive_path("m/0/1/2") {
        Ok(derived_key) => {
            println!("✓ Successfully derived public key using path m/0/1/2");
            println!("  Root public key: {}", hex::encode(dummy_root_key.as_bytes()));
            println!("  Derived public key: {}", hex::encode(derived_key.public_key().as_bytes()));
        }
        Err(e) => {
            println!("✗ Failed to derive public key: {}", e);
        }
    }
    
    Ok(())
}
